<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外贸定价计算器</title>
    <!-- 引入Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .calculator-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .input-section {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .result-section {
            padding: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        
        .result-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .profit-display {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .currency-symbol {
            font-size: 1.2rem;
            opacity: 0.8;
        }
        
        .calculation-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .input-group-text {
            background: #4CAF50;
            color: white;
            border: none;
        }
        
        @media (max-width: 768px) {
            .calculator-container {
                margin: 10px;
            }
            
            .profit-display {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="calculator-container">
            <!-- 头部标题 -->
            <div class="header">
                <h1><i class="fas fa-calculator me-2"></i>外贸定价计算器</h1>
                <p class="mb-0">Foreign Trade Pricing Calculator</p>
            </div>
            
            <!-- 输入区域 -->
            <div class="input-section">
                <!-- 基础成本信息 -->
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary mb-3"><i class="fas fa-calculator me-2"></i>基础成本信息</h6>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="importUnitCost" class="form-label">
                            <i class="fas fa-box me-1"></i>进货单价 (元)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" id="importUnitCost"
                                   placeholder="进货单价" step="0.01" min="0" value="11.80">
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="quantity" class="form-label">
                            <i class="fas fa-cubes me-1"></i>数量 (件)
                        </label>
                        <input type="number" class="form-control" id="quantity"
                               placeholder="数量" min="1" value="200">
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="transportCost" class="form-label">
                            <i class="fas fa-truck me-1"></i>运输费用 (元)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" id="transportCost"
                                   placeholder="运输费用" step="0.01" min="0" value="100">
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="laborCost" class="form-label">
                            <i class="fas fa-users me-1"></i>人工成本 (元)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" id="laborCost"
                                   placeholder="人工成本" step="0.01" min="0" value="42">
                        </div>
                    </div>
                </div>

                <!-- 汇率和定价 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="text-success mb-3"><i class="fas fa-exchange-alt me-2"></i>汇率与定价</h6>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="exchangeRate" class="form-label">
                            <i class="fas fa-coins me-1"></i>汇率 (欧元→人民币)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">€1=</span>
                            <input type="number" class="form-control" id="exchangeRate"
                                   placeholder="汇率" step="0.01" min="0" value="7.5">
                            <span class="input-group-text">¥</span>
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="sellingPriceEur" class="form-label">
                            <i class="fas fa-tag me-1"></i>客户报价 (欧元)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">€</span>
                            <input type="number" class="form-control" id="sellingPriceEur"
                                   placeholder="欧元报价" step="0.01" min="0" value="12.80">
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label for="targetProfitRate" class="form-label">
                            <i class="fas fa-percentage me-1"></i>目标利润率 (%)
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="targetProfitRate"
                                   placeholder="目标利润率" step="0.1" min="0" max="1000" value="20">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                </div>

                <!-- 税费设置 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="text-warning mb-3"><i class="fas fa-receipt me-2"></i>税费计算</h6>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="vatRate" class="form-label">
                            <i class="fas fa-percent me-1"></i>增值税率 (%)
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="vatRate"
                                   placeholder="增值税率" step="0.1" min="0" max="100" value="0">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="surchargeRate" class="form-label">
                            <i class="fas fa-plus me-1"></i>附加税率 (%)
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="surchargeRate"
                                   placeholder="附加税率" step="0.1" min="0" max="100" value="12">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="stampDutyRate" class="form-label">
                            <i class="fas fa-stamp me-1"></i>印花税率 (‰)
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="stampDutyRate"
                                   placeholder="印花税率" step="0.01" min="0" max="10" value="0.3">
                            <span class="input-group-text">‰</span>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="enterpriseTaxRate" class="form-label">
                            <i class="fas fa-building me-1"></i>企业所得税率 (%)
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="enterpriseTaxRate"
                                   placeholder="企业所得税率" step="0.1" min="0" max="100" value="25">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 结果显示区域 -->
            <div class="result-section">
                <div class="row">
                    <div class="col-md-4">
                        <div class="result-card">
                            <h5><i class="fas fa-chart-line me-2"></i>实际利润率</h5>
                            <div class="profit-display" id="actualProfitRate">0.00%</div>
                            <small>Actual Profit Rate (AR)</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="result-card">
                            <h5><i class="fas fa-money-bill-wave me-2"></i>实际利润</h5>
                            <div class="profit-display">
                                <span class="currency-symbol">¥</span>
                                <span id="actualProfit">0.00</span>
                            </div>
                            <small>Actual Profit Amount</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="result-card">
                            <h5><i class="fas fa-euro-sign me-2"></i>利润 (欧元)</h5>
                            <div class="profit-display">
                                <span class="currency-symbol">€</span>
                                <span id="actualProfitEur">0.00</span>
                            </div>
                            <small>Profit in EUR</small>
                        </div>
                    </div>
                </div>
                
                <!-- 计算详情 -->
                <div class="calculation-details">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-calculator me-2"></i>成本分析</h6>
                            <div class="detail-row">
                                <span>进货成本 (IC):</span>
                                <span>¥<span id="importCostTotal">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>总成本 (TC):</span>
                                <span>¥<span id="totalCost">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>销售额人民币 (SA):</span>
                                <span>¥<span id="salesAmountCny">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>销售额欧元:</span>
                                <span>€<span id="salesAmountEur">0.00</span></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6><i class="fas fa-receipt me-2"></i>税费明细</h6>
                            <div class="detail-row">
                                <span>增值税 (VAT):</span>
                                <span>¥<span id="vatAmount">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>附加税:</span>
                                <span>¥<span id="surchargeAmount">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>印花税:</span>
                                <span>¥<span id="stampDutyAmount">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>企业所得税:</span>
                                <span>¥<span id="enterpriseTaxAmount">0.00</span></span>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-pie me-2"></i>利润分析</h6>
                            <div class="detail-row">
                                <span>税前净利润:</span>
                                <span>¥<span id="profitBeforeTax">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>税后净利润:</span>
                                <span>¥<span id="profitAfterTax">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>利润差距:</span>
                                <span id="profitGap">0.00%</span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6><i class="fas fa-lightbulb me-2"></i>建议定价</h6>
                            <div class="detail-row">
                                <span>建议销售单价 (人民币):</span>
                                <span>¥<span id="suggestedPriceCny">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>建议销售单价 (欧元):</span>
                                <span>€<span id="suggestedPriceEur">0.00</span></span>
                            </div>
                            <div class="detail-row">
                                <span>建议销售总额:</span>
                                <span>¥<span id="suggestedTotalAmount">0.00</span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="mt-4 text-center">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="calculator.resetForm()">
                            <i class="fas fa-refresh me-1"></i>重置数据
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="calculator.loadSampleData()">
                            <i class="fas fa-lightbulb me-1"></i>示例数据
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="exportResults()">
                            <i class="fas fa-download me-1"></i>导出结果
                        </button>
                    </div>
                </div>

                <!-- 使用说明 -->
                <div class="mt-4">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>使用说明</h6>
                        <ul class="mb-0 small">
                            <li><strong>实时计算：</strong>输入任何数值后会自动计算利润率和相关数据</li>
                            <li><strong>双向调整：</strong>调整目标利润率时会自动更新建议销售单价</li>
                            <li><strong>颜色指示：</strong>红色表示亏损，橙色表示低利润，蓝色表示中等利润，绿色表示高利润</li>
                            <li><strong>快捷键：</strong>Ctrl+R 快速重置所有数据</li>
                            <li><strong>计算公式：</strong>利润率 = (销售额 - 总成本) / 总成本 × 100%</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 外贸定价计算器核心逻辑
        class ForeignTradePricingCalculator {
            constructor() {
                this.initializeElements();
                this.bindEvents();
                this.loadSampleData();
            }

            // 初始化DOM元素
            initializeElements() {
                this.inputs = {
                    importUnitCost: document.getElementById('importUnitCost'),
                    quantity: document.getElementById('quantity'),
                    transportCost: document.getElementById('transportCost'),
                    laborCost: document.getElementById('laborCost'),
                    exchangeRate: document.getElementById('exchangeRate'),
                    sellingPriceEur: document.getElementById('sellingPriceEur'),
                    targetProfitRate: document.getElementById('targetProfitRate'),
                    vatRate: document.getElementById('vatRate'),
                    surchargeRate: document.getElementById('surchargeRate'),
                    stampDutyRate: document.getElementById('stampDutyRate'),
                    enterpriseTaxRate: document.getElementById('enterpriseTaxRate')
                };

                this.displays = {
                    actualProfitRate: document.getElementById('actualProfitRate'),
                    actualProfit: document.getElementById('actualProfit'),
                    actualProfitEur: document.getElementById('actualProfitEur'),
                    importCostTotal: document.getElementById('importCostTotal'),
                    totalCost: document.getElementById('totalCost'),
                    salesAmountCny: document.getElementById('salesAmountCny'),
                    salesAmountEur: document.getElementById('salesAmountEur'),
                    vatAmount: document.getElementById('vatAmount'),
                    surchargeAmount: document.getElementById('surchargeAmount'),
                    stampDutyAmount: document.getElementById('stampDutyAmount'),
                    enterpriseTaxAmount: document.getElementById('enterpriseTaxAmount'),
                    profitBeforeTax: document.getElementById('profitBeforeTax'),
                    profitAfterTax: document.getElementById('profitAfterTax'),
                    profitGap: document.getElementById('profitGap'),
                    suggestedPriceCny: document.getElementById('suggestedPriceCny'),
                    suggestedPriceEur: document.getElementById('suggestedPriceEur'),
                    suggestedTotalAmount: document.getElementById('suggestedTotalAmount')
                };
            }

            // 绑定事件监听器
            bindEvents() {
                // 为所有输入框添加实时计算事件
                Object.values(this.inputs).forEach(input => {
                    input.addEventListener('input', () => this.calculate());
                    input.addEventListener('change', () => this.calculate());
                });

                // 目标利润率变化时自动调整销售单价
                this.inputs.targetProfitRate.addEventListener('input', () => {
                    this.autoAdjustSellingPrice();
                });

                // 添加键盘快捷键支持
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.key === 'r') {
                        e.preventDefault();
                        this.resetForm();
                    }
                });
            }

            // 获取输入值
            getInputValues() {
                return {
                    importUnitCost: parseFloat(this.inputs.importUnitCost.value) || 0,
                    quantity: parseInt(this.inputs.quantity.value) || 1,
                    transportCost: parseFloat(this.inputs.transportCost.value) || 0,
                    laborCost: parseFloat(this.inputs.laborCost.value) || 0,
                    exchangeRate: parseFloat(this.inputs.exchangeRate.value) || 7.5,
                    sellingPriceEur: parseFloat(this.inputs.sellingPriceEur.value) || 0,
                    targetProfitRate: parseFloat(this.inputs.targetProfitRate.value) || 0,
                    vatRate: parseFloat(this.inputs.vatRate.value) || 0,
                    surchargeRate: parseFloat(this.inputs.surchargeRate.value) || 0,
                    stampDutyRate: parseFloat(this.inputs.stampDutyRate.value) || 0,
                    enterpriseTaxRate: parseFloat(this.inputs.enterpriseTaxRate.value) || 25
                };
            }

            // 核心计算函数
            calculate() {
                const values = this.getInputValues();

                // 1. 计算进货成本 (IC = C × Q)
                const importCostTotal = values.importUnitCost * values.quantity;

                // 2. 计算总成本 (TC = IC + T + L)
                const totalCost = importCostTotal + values.transportCost + values.laborCost;

                // 3. 计算销售额 (SA = P × Q × 汇率)
                const salesAmountCny = values.sellingPriceEur * values.quantity * values.exchangeRate;
                const salesAmountEur = values.sellingPriceEur * values.quantity;

                // 4. 计算税费
                // 4.1 增值税 (VAT = SA × 增值税率)
                const vatAmount = salesAmountCny * (values.vatRate / 100);

                // 4.2 附加税 (Surcharge = VAT × 附加税率)
                const surchargeAmount = vatAmount * (values.surchargeRate / 100);

                // 4.3 印花税 (Stamp Duty = SA × 印花税率)
                const stampDutyAmount = salesAmountCny * (values.stampDutyRate / 1000);

                // 5. 计算税前利润
                const profitBeforeTax = salesAmountCny - totalCost;

                // 6. 计算企业所得税
                const taxableProfit = Math.max(profitBeforeTax - vatAmount - surchargeAmount - stampDutyAmount, 0);
                const enterpriseTaxAmount = taxableProfit * (values.enterpriseTaxRate / 100);

                // 7. 计算税后净利润
                const profitAfterTax = profitBeforeTax - vatAmount - surchargeAmount - stampDutyAmount - enterpriseTaxAmount;

                // 8. 计算实际利润率 (AR = 税后净利润 / 总成本 × 100)
                const actualProfitRate = totalCost > 0 ? (profitAfterTax / totalCost) * 100 : 0;

                // 9. 计算欧元利润
                const actualProfitEur = profitAfterTax / values.exchangeRate;

                // 10. 计算建议销售单价 (根据目标利润率)
                const suggestedTotalCostWithProfit = totalCost * (1 + values.targetProfitRate / 100);
                const suggestedPriceCny = suggestedTotalCostWithProfit / values.quantity;
                const suggestedPriceEur = suggestedPriceCny / values.exchangeRate;
                const suggestedTotalAmount = suggestedTotalCostWithProfit;

                // 11. 计算利润差距 (目标利润率 - 实际利润率)
                const profitGap = values.targetProfitRate - actualProfitRate;

                // 更新显示结果
                this.updateDisplays({
                    actualProfitRate,
                    actualProfit: profitAfterTax,
                    actualProfitEur,
                    importCostTotal,
                    totalCost,
                    salesAmountCny,
                    salesAmountEur,
                    vatAmount,
                    surchargeAmount,
                    stampDutyAmount,
                    enterpriseTaxAmount,
                    profitBeforeTax,
                    profitAfterTax,
                    profitGap,
                    suggestedPriceCny,
                    suggestedPriceEur,
                    suggestedTotalAmount
                });

                // 更新视觉效果
                this.updateVisualEffects(actualProfitRate, profitGap);
            }

            // 更新显示结果
            updateDisplays(results) {
                // 主要结果显示
                this.displays.actualProfitRate.textContent = results.actualProfitRate.toFixed(2) + '%';
                this.displays.actualProfit.textContent = results.actualProfit.toFixed(2);
                this.displays.actualProfitEur.textContent = results.actualProfitEur.toFixed(2);

                // 成本分析
                this.displays.importCostTotal.textContent = results.importCostTotal.toFixed(2);
                this.displays.totalCost.textContent = results.totalCost.toFixed(2);
                this.displays.salesAmountCny.textContent = results.salesAmountCny.toFixed(2);
                this.displays.salesAmountEur.textContent = results.salesAmountEur.toFixed(2);

                // 税费明细
                this.displays.vatAmount.textContent = results.vatAmount.toFixed(2);
                this.displays.surchargeAmount.textContent = results.surchargeAmount.toFixed(2);
                this.displays.stampDutyAmount.textContent = results.stampDutyAmount.toFixed(2);
                this.displays.enterpriseTaxAmount.textContent = results.enterpriseTaxAmount.toFixed(2);

                // 利润分析
                this.displays.profitBeforeTax.textContent = results.profitBeforeTax.toFixed(2);
                this.displays.profitAfterTax.textContent = results.profitAfterTax.toFixed(2);

                // 建议定价
                this.displays.suggestedPriceCny.textContent = results.suggestedPriceCny.toFixed(2);
                this.displays.suggestedPriceEur.textContent = results.suggestedPriceEur.toFixed(2);
                this.displays.suggestedTotalAmount.textContent = results.suggestedTotalAmount.toFixed(2);

                // 利润差距显示优化
                const gapText = results.profitGap > 0 ?
                    `需提高 ${results.profitGap.toFixed(2)}%` :
                    `超出目标 ${Math.abs(results.profitGap).toFixed(2)}%`;
                this.displays.profitGap.textContent = gapText;
            }

            // 更新视觉效果
            updateVisualEffects(actualProfitRate, profitGap) {
                const profitRateElement = this.displays.actualProfitRate.parentElement;
                const profitElement = this.displays.actualProfit.parentElement;

                // 根据利润率设置颜色
                if (actualProfitRate < 0) {
                    profitRateElement.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a52)';
                    profitElement.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a52)';
                } else if (actualProfitRate < 10) {
                    profitRateElement.style.background = 'linear-gradient(135deg, #ffa726, #ff9800)';
                    profitElement.style.background = 'linear-gradient(135deg, #ffa726, #ff9800)';
                } else if (actualProfitRate < 20) {
                    profitRateElement.style.background = 'linear-gradient(135deg, #42a5f5, #1e88e5)';
                    profitElement.style.background = 'linear-gradient(135deg, #42a5f5, #1e88e5)';
                } else {
                    profitRateElement.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                    profitElement.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                }

                // 利润差距颜色
                const gapElement = this.displays.profitGap;
                if (Math.abs(profitGap) < 1) {
                    gapElement.style.color = '#4CAF50';
                } else if (Math.abs(profitGap) < 5) {
                    gapElement.style.color = '#ff9800';
                } else {
                    gapElement.style.color = '#f44336';
                }
            }

            // 自动调整销售单价
            autoAdjustSellingPrice() {
                const values = this.getInputValues();
                const totalCost = (values.importUnitCost * values.quantity) + values.transportCost + values.laborCost;

                if (totalCost > 0 && values.targetProfitRate > 0) {
                    const suggestedPriceCny = (totalCost * (1 + values.targetProfitRate / 100)) / values.quantity;
                    const suggestedPriceEur = suggestedPriceCny / values.exchangeRate;
                    this.inputs.sellingPriceEur.value = suggestedPriceEur.toFixed(2);
                }

                this.calculate();
            }

            // 重置表单
            resetForm() {
                Object.values(this.inputs).forEach(input => {
                    input.value = '';
                });
                this.inputs.quantity.value = '200';
                this.inputs.exchangeRate.value = '7.5';
                this.inputs.vatRate.value = '0';
                this.inputs.surchargeRate.value = '12';
                this.inputs.stampDutyRate.value = '0.3';
                this.inputs.enterpriseTaxRate.value = '25';
                this.inputs.targetProfitRate.value = '20';
                this.calculate();
            }

            // 加载示例数据
            loadSampleData() {
                this.inputs.importUnitCost.value = '11.80';
                this.inputs.quantity.value = '200';
                this.inputs.transportCost.value = '100';
                this.inputs.laborCost.value = '42';
                this.inputs.exchangeRate.value = '7.5';
                this.inputs.sellingPriceEur.value = '12.80';
                this.inputs.targetProfitRate.value = '20';
                this.inputs.vatRate.value = '0';
                this.inputs.surchargeRate.value = '12';
                this.inputs.stampDutyRate.value = '0.3';
                this.inputs.enterpriseTaxRate.value = '25';
                this.calculate();
            }

            // 获取计算结果（用于导出）
            getCalculationResults() {
                const values = this.getInputValues();

                const importCostTotal = values.importUnitCost * values.quantity;
                const totalCost = importCostTotal + values.transportCost + values.laborCost;
                const salesAmountCny = values.sellingPriceEur * values.quantity * values.exchangeRate;
                const salesAmountEur = values.sellingPriceEur * values.quantity;

                const vatAmount = salesAmountCny * (values.vatRate / 100);
                const surchargeAmount = vatAmount * (values.surchargeRate / 100);
                const stampDutyAmount = salesAmountCny * (values.stampDutyRate / 1000);

                const profitBeforeTax = salesAmountCny - totalCost;
                const taxableProfit = Math.max(profitBeforeTax - vatAmount - surchargeAmount - stampDutyAmount, 0);
                const enterpriseTaxAmount = taxableProfit * (values.enterpriseTaxRate / 100);
                const profitAfterTax = profitBeforeTax - vatAmount - surchargeAmount - stampDutyAmount - enterpriseTaxAmount;

                const actualProfitRate = totalCost > 0 ? (profitAfterTax / totalCost) * 100 : 0;
                const actualProfitEur = profitAfterTax / values.exchangeRate;

                const suggestedTotalCostWithProfit = totalCost * (1 + values.targetProfitRate / 100);
                const suggestedPriceCny = suggestedTotalCostWithProfit / values.quantity;
                const suggestedPriceEur = suggestedPriceCny / values.exchangeRate;

                const profitGap = values.targetProfitRate - actualProfitRate;

                return {
                    importCostTotal,
                    totalCost,
                    salesAmountCny,
                    salesAmountEur,
                    vatAmount,
                    surchargeAmount,
                    stampDutyAmount,
                    enterpriseTaxAmount,
                    profitBeforeTax,
                    profitAfterTax,
                    actualProfit: profitAfterTax,
                    actualProfitRate,
                    actualProfitEur,
                    suggestedPriceCny,
                    suggestedPriceEur,
                    suggestedTotalAmount: suggestedTotalCostWithProfit,
                    profitGap
                };
            }
        }

        // 全局计算器实例
        let calculator;

        // 页面加载完成后初始化计算器
        document.addEventListener('DOMContentLoaded', function() {
            calculator = new ForeignTradePricingCalculator();
        });

        // 导出结果功能
        function exportResults() {
            const values = calculator.getInputValues();
            const results = calculator.getCalculationResults();

            const exportData = {
                输入数据: {
                    进货单价: `¥${values.importUnitCost}`,
                    数量: `${values.quantity}件`,
                    运输费用: `¥${values.transportCost}`,
                    人工成本: `¥${values.laborCost}`,
                    汇率: `€1 = ¥${values.exchangeRate}`,
                    客户报价: `€${values.sellingPriceEur}`,
                    目标利润率: `${values.targetProfitRate}%`,
                    增值税率: `${values.vatRate}%`,
                    附加税率: `${values.surchargeRate}%`,
                    印花税率: `${values.stampDutyRate}‰`,
                    企业所得税率: `${values.enterpriseTaxRate}%`
                },
                计算结果: {
                    进货成本总计: `¥${results.importCostTotal.toFixed(2)}`,
                    总成本: `¥${results.totalCost.toFixed(2)}`,
                    销售额人民币: `¥${results.salesAmountCny.toFixed(2)}`,
                    销售额欧元: `€${results.salesAmountEur.toFixed(2)}`,
                    增值税: `¥${results.vatAmount.toFixed(2)}`,
                    附加税: `¥${results.surchargeAmount.toFixed(2)}`,
                    印花税: `¥${results.stampDutyAmount.toFixed(2)}`,
                    企业所得税: `¥${results.enterpriseTaxAmount.toFixed(2)}`,
                    税前利润: `¥${results.profitBeforeTax.toFixed(2)}`,
                    税后净利润: `¥${results.profitAfterTax.toFixed(2)}`,
                    实际利润率: `${results.actualProfitRate.toFixed(2)}%`,
                    利润欧元: `€${results.actualProfitEur.toFixed(2)}`,
                    建议销售单价人民币: `¥${results.suggestedPriceCny.toFixed(2)}`,
                    建议销售单价欧元: `€${results.suggestedPriceEur.toFixed(2)}`,
                    利润差距: `${results.profitGap.toFixed(2)}%`
                },
                导出时间: new Date().toLocaleString('zh-CN')
            };

            // 创建并下载JSON文件
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `外贸定价计算结果_${new Date().toISOString().slice(0,10)}.json`;
            link.click();
            URL.revokeObjectURL(url);

            // 显示成功提示
            showNotification('计算结果已导出！', 'success');
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // 添加数据验证和格式化功能
        function formatCurrency(value) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 2
            }).format(value);
        }

        function formatPercentage(value) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'percent',
                minimumFractionDigits: 2
            }).format(value / 100);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            .btn-group .btn {
                transition: all 0.3s ease;
            }

            .btn-group .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }

            .result-card {
                transition: all 0.3s ease;
            }

            .result-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
