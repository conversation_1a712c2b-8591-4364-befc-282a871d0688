<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外贸定价计算器</title>
    <!-- 引入Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .calculator-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .input-section {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .result-section {
            padding: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        
        .result-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .profit-display {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .currency-symbol {
            font-size: 1.2rem;
            opacity: 0.8;
        }
        
        .calculation-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .input-group-text {
            background: #4CAF50;
            color: white;
            border: none;
        }
        
        @media (max-width: 768px) {
            .calculator-container {
                margin: 10px;
            }
            
            .profit-display {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="calculator-container">
            <!-- 头部标题 -->
            <div class="header">
                <h1><i class="fas fa-calculator me-2"></i>外贸定价计算器</h1>
                <p class="mb-0">Foreign Trade Pricing Calculator</p>
            </div>
            
            <!-- 输入区域 -->
            <div class="input-section">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="importCost" class="form-label">
                            <i class="fas fa-box me-1"></i>进货成本 (元)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" id="importCost" 
                                   placeholder="请输入进货成本" step="0.01" min="0">
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="quantity" class="form-label">
                            <i class="fas fa-cubes me-1"></i>数量 (件)
                        </label>
                        <input type="number" class="form-control" id="quantity" 
                               placeholder="请输入数量" min="1" value="1">
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="transportCost" class="form-label">
                            <i class="fas fa-truck me-1"></i>运输费用 (元)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" id="transportCost" 
                                   placeholder="请输入运输费用" step="0.01" min="0" value="0">
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="laborCost" class="form-label">
                            <i class="fas fa-users me-1"></i>人工成本 (元)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" id="laborCost" 
                                   placeholder="请输入人工成本" step="0.01" min="0" value="0">
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="sellingPrice" class="form-label">
                            <i class="fas fa-tag me-1"></i>销售单价 (元)
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" id="sellingPrice" 
                                   placeholder="请输入销售单价" step="0.01" min="0">
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="targetProfitRate" class="form-label">
                            <i class="fas fa-percentage me-1"></i>目标利润率 (%)
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="targetProfitRate" 
                                   placeholder="请输入目标利润率" step="0.1" min="0" max="1000">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 结果显示区域 -->
            <div class="result-section">
                <div class="row">
                    <div class="col-md-6">
                        <div class="result-card">
                            <h5><i class="fas fa-chart-line me-2"></i>实际利润率</h5>
                            <div class="profit-display" id="actualProfitRate">0.00%</div>
                            <small>Actual Profit Rate</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="result-card">
                            <h5><i class="fas fa-money-bill-wave me-2"></i>实际利润</h5>
                            <div class="profit-display">
                                <span class="currency-symbol">¥</span>
                                <span id="actualProfit">0.00</span>
                            </div>
                            <small>Actual Profit Amount</small>
                        </div>
                    </div>
                </div>
                
                <!-- 计算详情 -->
                <div class="calculation-details">
                    <h6><i class="fas fa-list-ul me-2"></i>计算详情</h6>
                    <div class="detail-row">
                        <span>总成本 (TC):</span>
                        <span>¥<span id="totalCost">0.00</span></span>
                    </div>
                    <div class="detail-row">
                        <span>销售额 (SA):</span>
                        <span>¥<span id="salesAmount">0.00</span></span>
                    </div>
                    <div class="detail-row">
                        <span>建议销售单价:</span>
                        <span>¥<span id="suggestedPrice">0.00</span></span>
                    </div>
                    <div class="detail-row">
                        <span>利润差距:</span>
                        <span id="profitGap">0.00%</span>
                    </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="mt-4 text-center">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="calculator.resetForm()">
                            <i class="fas fa-refresh me-1"></i>重置数据
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="calculator.loadSampleData()">
                            <i class="fas fa-lightbulb me-1"></i>示例数据
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="exportResults()">
                            <i class="fas fa-download me-1"></i>导出结果
                        </button>
                    </div>
                </div>

                <!-- 使用说明 -->
                <div class="mt-4">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>使用说明</h6>
                        <ul class="mb-0 small">
                            <li><strong>实时计算：</strong>输入任何数值后会自动计算利润率和相关数据</li>
                            <li><strong>双向调整：</strong>调整目标利润率时会自动更新建议销售单价</li>
                            <li><strong>颜色指示：</strong>红色表示亏损，橙色表示低利润，蓝色表示中等利润，绿色表示高利润</li>
                            <li><strong>快捷键：</strong>Ctrl+R 快速重置所有数据</li>
                            <li><strong>计算公式：</strong>利润率 = (销售额 - 总成本) / 总成本 × 100%</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 外贸定价计算器核心逻辑
        class ForeignTradePricingCalculator {
            constructor() {
                this.initializeElements();
                this.bindEvents();
                this.loadSampleData();
            }

            // 初始化DOM元素
            initializeElements() {
                this.inputs = {
                    importCost: document.getElementById('importCost'),
                    quantity: document.getElementById('quantity'),
                    transportCost: document.getElementById('transportCost'),
                    laborCost: document.getElementById('laborCost'),
                    sellingPrice: document.getElementById('sellingPrice'),
                    targetProfitRate: document.getElementById('targetProfitRate')
                };

                this.displays = {
                    actualProfitRate: document.getElementById('actualProfitRate'),
                    actualProfit: document.getElementById('actualProfit'),
                    totalCost: document.getElementById('totalCost'),
                    salesAmount: document.getElementById('salesAmount'),
                    suggestedPrice: document.getElementById('suggestedPrice'),
                    profitGap: document.getElementById('profitGap')
                };
            }

            // 绑定事件监听器
            bindEvents() {
                // 为所有输入框添加实时计算事件
                Object.values(this.inputs).forEach(input => {
                    input.addEventListener('input', () => this.calculate());
                    input.addEventListener('change', () => this.calculate());
                });

                // 目标利润率变化时自动调整销售单价
                this.inputs.targetProfitRate.addEventListener('input', () => {
                    this.autoAdjustSellingPrice();
                });

                // 添加键盘快捷键支持
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.key === 'r') {
                        e.preventDefault();
                        this.resetForm();
                    }
                });
            }

            // 获取输入值
            getInputValues() {
                return {
                    importCost: parseFloat(this.inputs.importCost.value) || 0,
                    quantity: parseInt(this.inputs.quantity.value) || 1,
                    transportCost: parseFloat(this.inputs.transportCost.value) || 0,
                    laborCost: parseFloat(this.inputs.laborCost.value) || 0,
                    sellingPrice: parseFloat(this.inputs.sellingPrice.value) || 0,
                    targetProfitRate: parseFloat(this.inputs.targetProfitRate.value) || 0
                };
            }

            // 核心计算函数
            calculate() {
                const values = this.getInputValues();

                // 1. 计算进货成本 (IC = C × Q)
                const importCostTotal = values.importCost * values.quantity;

                // 2. 计算总成本 (TC = IC + T + L)
                const totalCost = importCostTotal + values.transportCost + values.laborCost;

                // 3. 计算销售额 (SA = P × Q)
                const salesAmount = values.sellingPrice * values.quantity;

                // 4. 计算实际利润 (实际利润 = 销售额 - 总成本)
                const actualProfit = salesAmount - totalCost;

                // 5. 计算实际利润率 (AR = 实际利润 / 总成本 × 100)
                const actualProfitRate = totalCost > 0 ? (actualProfit / totalCost) * 100 : 0;

                // 6. 计算建议销售单价 (根据目标利润率)
                const suggestedPrice = totalCost > 0 ? (totalCost * (1 + values.targetProfitRate / 100)) / values.quantity : 0;

                // 7. 计算利润差距 (目标利润率 - 实际利润率)
                const profitGap = values.targetProfitRate - actualProfitRate;

                // 更新显示结果
                this.updateDisplays({
                    actualProfitRate,
                    actualProfit,
                    totalCost,
                    salesAmount,
                    suggestedPrice,
                    profitGap
                });

                // 更新视觉效果
                this.updateVisualEffects(actualProfitRate, profitGap);
            }

            // 更新显示结果
            updateDisplays(results) {
                this.displays.actualProfitRate.textContent = results.actualProfitRate.toFixed(2) + '%';
                this.displays.actualProfit.textContent = results.actualProfit.toFixed(2);
                this.displays.totalCost.textContent = results.totalCost.toFixed(2);
                this.displays.salesAmount.textContent = results.salesAmount.toFixed(2);
                this.displays.suggestedPrice.textContent = results.suggestedPrice.toFixed(2);

                // 利润差距显示优化
                const gapText = results.profitGap > 0 ?
                    `需提高 ${results.profitGap.toFixed(2)}%` :
                    `超出目标 ${Math.abs(results.profitGap).toFixed(2)}%`;
                this.displays.profitGap.textContent = gapText;
            }

            // 更新视觉效果
            updateVisualEffects(actualProfitRate, profitGap) {
                const profitRateElement = this.displays.actualProfitRate.parentElement;
                const profitElement = this.displays.actualProfit.parentElement;

                // 根据利润率设置颜色
                if (actualProfitRate < 0) {
                    profitRateElement.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a52)';
                    profitElement.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a52)';
                } else if (actualProfitRate < 10) {
                    profitRateElement.style.background = 'linear-gradient(135deg, #ffa726, #ff9800)';
                    profitElement.style.background = 'linear-gradient(135deg, #ffa726, #ff9800)';
                } else if (actualProfitRate < 20) {
                    profitRateElement.style.background = 'linear-gradient(135deg, #42a5f5, #1e88e5)';
                    profitElement.style.background = 'linear-gradient(135deg, #42a5f5, #1e88e5)';
                } else {
                    profitRateElement.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                    profitElement.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                }

                // 利润差距颜色
                const gapElement = this.displays.profitGap;
                if (Math.abs(profitGap) < 1) {
                    gapElement.style.color = '#4CAF50';
                } else if (Math.abs(profitGap) < 5) {
                    gapElement.style.color = '#ff9800';
                } else {
                    gapElement.style.color = '#f44336';
                }
            }

            // 自动调整销售单价
            autoAdjustSellingPrice() {
                const values = this.getInputValues();
                const totalCost = (values.importCost * values.quantity) + values.transportCost + values.laborCost;

                if (totalCost > 0 && values.targetProfitRate > 0) {
                    const suggestedPrice = (totalCost * (1 + values.targetProfitRate / 100)) / values.quantity;
                    this.inputs.sellingPrice.value = suggestedPrice.toFixed(2);
                }

                this.calculate();
            }

            // 重置表单
            resetForm() {
                Object.values(this.inputs).forEach(input => {
                    input.value = '';
                });
                this.inputs.quantity.value = '1';
                this.calculate();
            }

            // 加载示例数据
            loadSampleData() {
                this.inputs.importCost.value = '100';
                this.inputs.quantity.value = '10';
                this.inputs.transportCost.value = '50';
                this.inputs.laborCost.value = '30';
                this.inputs.sellingPrice.value = '150';
                this.inputs.targetProfitRate.value = '20';
                this.calculate();
            }

            // 获取计算结果（用于导出）
            getCalculationResults() {
                const values = this.getInputValues();

                const importCostTotal = values.importCost * values.quantity;
                const totalCost = importCostTotal + values.transportCost + values.laborCost;
                const salesAmount = values.sellingPrice * values.quantity;
                const actualProfit = salesAmount - totalCost;
                const actualProfitRate = totalCost > 0 ? (actualProfit / totalCost) * 100 : 0;
                const suggestedPrice = totalCost > 0 ? (totalCost * (1 + values.targetProfitRate / 100)) / values.quantity : 0;
                const profitGap = values.targetProfitRate - actualProfitRate;

                return {
                    totalCost,
                    salesAmount,
                    actualProfit,
                    actualProfitRate,
                    suggestedPrice,
                    profitGap
                };
            }
        }

        // 全局计算器实例
        let calculator;

        // 页面加载完成后初始化计算器
        document.addEventListener('DOMContentLoaded', function() {
            calculator = new ForeignTradePricingCalculator();
        });

        // 导出结果功能
        function exportResults() {
            const values = calculator.getInputValues();
            const results = calculator.getCalculationResults();

            const exportData = {
                输入数据: {
                    进货成本: `¥${values.importCost}`,
                    数量: `${values.quantity}件`,
                    运输费用: `¥${values.transportCost}`,
                    人工成本: `¥${values.laborCost}`,
                    销售单价: `¥${values.sellingPrice}`,
                    目标利润率: `${values.targetProfitRate}%`
                },
                计算结果: {
                    总成本: `¥${results.totalCost.toFixed(2)}`,
                    销售额: `¥${results.salesAmount.toFixed(2)}`,
                    实际利润: `¥${results.actualProfit.toFixed(2)}`,
                    实际利润率: `${results.actualProfitRate.toFixed(2)}%`,
                    建议销售单价: `¥${results.suggestedPrice.toFixed(2)}`,
                    利润差距: `${results.profitGap.toFixed(2)}%`
                },
                导出时间: new Date().toLocaleString('zh-CN')
            };

            // 创建并下载JSON文件
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `外贸定价计算结果_${new Date().toISOString().slice(0,10)}.json`;
            link.click();
            URL.revokeObjectURL(url);

            // 显示成功提示
            showNotification('计算结果已导出！', 'success');
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // 添加数据验证和格式化功能
        function formatCurrency(value) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 2
            }).format(value);
        }

        function formatPercentage(value) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'percent',
                minimumFractionDigits: 2
            }).format(value / 100);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            .btn-group .btn {
                transition: all 0.3s ease;
            }

            .btn-group .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }

            .result-card {
                transition: all 0.3s ease;
            }

            .result-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
